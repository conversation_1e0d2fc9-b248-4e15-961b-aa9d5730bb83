#!/usr/bin/env python3
"""
StockTwits数据收集器快速启动脚本

这是一个简化的启动脚本，提供了常用的数据收集场景，
让用户可以快速开始使用StockTwits数据收集器。

作者: AI Assistant
创建时间: 2025-06-26
"""

import os
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path

# 导入主要的收集器
from stocktwits_data_collector import StockTwitsCollector, CollectorConfig

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 StockTwits 社交媒体数据收集器")
    print("=" * 60)
    print("📊 支持股票: AAPL, MSFT, NVDA 等")
    print("📅 时间范围: 2025年1月1日 - 2025年6月15日")
    print("💾 数据格式: JSON (兼容 social_media_analyst)")
    print("=" * 60)

def show_menu():
    """显示主菜单"""
    print("\n📋 请选择操作:")
    print("1. 🔄 收集所有股票数据 (AAPL, MSFT, NVDA)")
    print("2. 📈 收集单个股票数据")
    print("3. ⚙️  自定义配置收集")
    print("4. 📊 查看已收集数据摘要")
    print("5. 🧪 运行测试")
    print("6. 📖 查看使用说明")
    print("0. 🚪 退出")
    print("-" * 40)

def collect_all_stocks():
    """收集所有默认股票的数据"""
    print("\n🔄 开始收集所有股票数据...")
    
    config = CollectorConfig(
        tickers=["AAPL", "MSFT", "NVDA"],
        start_date="2025-01-01",
        end_date="2025-06-15",
        request_delay=2.0
    )
    
    collector = StockTwitsCollector(config)
    
    try:
        collector.collect_historical_data()
        collector.create_summary_report()
        print("\n✅ 数据收集完成！")
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断了数据收集")
        collector.create_summary_report()
    except Exception as e:
        print(f"\n❌ 收集过程中发生错误: {e}")

def collect_single_stock():
    """收集单个股票的数据"""
    print("\n📈 收集单个股票数据")
    
    # 获取用户输入
    ticker = input("请输入股票代码 (例如: AAPL): ").upper().strip()
    if not ticker:
        print("❌ 股票代码不能为空")
        return
    
    start_date = input("请输入开始日期 (YYYY-MM-DD, 默认: 2025-01-01): ").strip()
    if not start_date:
        start_date = "2025-01-01"
    
    end_date = input("请输入结束日期 (YYYY-MM-DD, 默认: 2025-06-15): ").strip()
    if not end_date:
        end_date = "2025-06-15"
    
    print(f"\n🎯 开始收集 {ticker} 从 {start_date} 到 {end_date} 的数据...")
    
    config = CollectorConfig(
        tickers=[ticker],
        start_date=start_date,
        end_date=end_date,
        request_delay=2.0
    )
    
    collector = StockTwitsCollector(config)
    
    try:
        collector.collect_historical_data()
        collector.create_summary_report()
        print(f"\n✅ {ticker} 数据收集完成！")
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断了 {ticker} 数据收集")
        collector.create_summary_report()
    except Exception as e:
        print(f"\n❌ 收集 {ticker} 数据时发生错误: {e}")

def custom_collection():
    """自定义配置收集"""
    print("\n⚙️ 自定义配置数据收集")
    
    # 获取股票列表
    tickers_input = input("请输入股票代码 (用空格分隔, 例如: AAPL MSFT NVDA): ").upper().strip()
    if not tickers_input:
        tickers = ["AAPL", "MSFT", "NVDA"]
        print("使用默认股票: AAPL, MSFT, NVDA")
    else:
        tickers = tickers_input.split()
    
    # 获取日期范围
    start_date = input("开始日期 (YYYY-MM-DD, 默认: 2025-01-01): ").strip()
    if not start_date:
        start_date = "2025-01-01"
    
    end_date = input("结束日期 (YYYY-MM-DD, 默认: 2025-06-15): ").strip()
    if not end_date:
        end_date = "2025-06-15"
    
    # 获取请求延迟
    delay_input = input("请求间隔秒数 (默认: 2.0): ").strip()
    try:
        delay = float(delay_input) if delay_input else 2.0
    except ValueError:
        delay = 2.0
        print("使用默认延迟: 2.0秒")
    
    # 获取数据目录
    data_dir = input("数据保存目录 (默认: social_media_data): ").strip()
    if not data_dir:
        data_dir = "social_media_data"
    
    print(f"\n📋 配置摘要:")
    print(f"   股票代码: {', '.join(tickers)}")
    print(f"   时间范围: {start_date} 到 {end_date}")
    print(f"   请求延迟: {delay} 秒")
    print(f"   保存目录: {data_dir}")
    
    confirm = input("\n确认开始收集? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 已取消收集")
        return
    
    config = CollectorConfig(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        request_delay=delay,
        base_data_dir=data_dir
    )
    
    collector = StockTwitsCollector(config)
    
    try:
        collector.collect_historical_data()
        collector.create_summary_report()
        print("\n✅ 自定义数据收集完成！")
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断了数据收集")
        collector.create_summary_report()
    except Exception as e:
        print(f"\n❌ 收集过程中发生错误: {e}")

def show_data_summary():
    """显示数据摘要"""
    print("\n📊 数据摘要报告")
    
    config = CollectorConfig()
    collector = StockTwitsCollector(config)
    collector.create_summary_report()

def run_tests():
    """运行测试"""
    print("\n🧪 运行快速测试...")
    
    try:
        from test_stocktwits_collector import run_quick_test
        run_quick_test()
    except ImportError:
        print("❌ 测试模块未找到，请确保 test_stocktwits_collector.py 文件存在")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

def show_usage_guide():
    """显示使用说明"""
    print("\n📖 使用说明")
    print("-" * 40)
    print("1. 数据收集:")
    print("   - 选择菜单选项1收集所有默认股票数据")
    print("   - 选择菜单选项2收集单个股票数据")
    print("   - 选择菜单选项3进行自定义配置")
    print()
    print("2. 数据格式:")
    print("   - 数据保存在 social_media_data/ 目录下")
    print("   - 每个股票有独立的子目录")
    print("   - 按日期保存为 stocktwits_YYYY-MM-DD.json 格式")
    print()
    print("3. 兼容性:")
    print("   - 输出格式与 social_media_analyst 完全兼容")
    print("   - 包含情感分析、参与度分数等关键字段")
    print()
    print("4. 注意事项:")
    print("   - 建议设置2秒以上的请求间隔避免API限制")
    print("   - 大量数据收集可能需要较长时间")
    print("   - 可随时按 Ctrl+C 中断收集过程")
    print()
    print("5. 故障排除:")
    print("   - 如遇API限制，增加请求间隔时间")
    print("   - 检查网络连接和防火墙设置")
    print("   - 查看 stocktwits_collector.log 日志文件")

def main():
    """主函数"""
    print_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用 StockTwits 数据收集器！")
                break
            elif choice == "1":
                collect_all_stocks()
            elif choice == "2":
                collect_single_stock()
            elif choice == "3":
                custom_collection()
            elif choice == "4":
                show_data_summary()
            elif choice == "5":
                run_tests()
            elif choice == "6":
                show_usage_guide()
            else:
                print("❌ 无效选择，请输入 0-6 之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"\n❌ 程序运行错误: {e}")
            print("请重试或联系技术支持")

if __name__ == "__main__":
    main()
