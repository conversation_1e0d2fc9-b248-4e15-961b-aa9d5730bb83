from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.tools.api import get_company_news
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, List
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta


class NewsAnalysisSignal(BaseModel):
    """Pydantic model for news analysis LLM output"""
    signal: str = Field(description="Trading signal: 'bullish', 'bearish', or 'neutral'")
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    sentiment_analysis: str = Field(description="Analysis of overall news sentiment and tone")
    event_impact_analysis: str = Field(description="Analysis of specific events and their market impact")
    news_frequency_analysis: str = Field(description="Analysis of news frequency and attention patterns")
    trend_analysis: str = Field(description="Analysis of sentiment trends over time")
    credibility_analysis: str = Field(description="Analysis of news sources and credibility factors")


##### News Analyst Agent #####
def news_analyst_agent(state: AgentState):
    """Analyzes news sentiment and trends using LLM to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    model_name = state["metadata"]["model_name"]
    model_provider = state["metadata"]["model_provider"]

    # Initialize news analysis for each ticker
    news_analysis = {}

    for ticker in tickers:
        progress.update_status("news_analyst_agent", ticker, "Fetching company news")

        # Get company news for sentiment analysis
        company_news = get_company_news(
            ticker=ticker,
            end_date=end_date,
            limit=100,  # Get more news for better analysis
            agent_name="news_analyst_agent",
        )

        if not company_news:
            progress.update_status("news_analyst_agent", ticker, "Failed: No news data found")
            continue

        progress.update_status("news_analyst_agent", ticker, "Preparing news data for LLM analysis")

        # Prepare news data for LLM analysis
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        recent_threshold = end_datetime - timedelta(days=7)  # Last 7 days

        # Categorize news by time periods
        recent_news = []
        older_news = []

        for news in company_news:
            if hasattr(news, 'date') and news.date:
                try:
                    news_date = datetime.strptime(news.date, "%Y-%m-%d")
                    if news_date >= recent_threshold:
                        recent_news.append(news)
                    else:
                        older_news.append(news)
                except:
                    older_news.append(news)
            else:
                older_news.append(news)

        # Prepare structured news data for LLM
        news_data = {
            "total_news_count": len(company_news),
            "recent_news_count": len(recent_news),
            "older_news_count": len(older_news),
            "recent_news": [],
            "older_news_sample": [],
            "sentiment_distribution": {
                "positive": 0,
                "negative": 0,
                "neutral": 0
            },
            "key_topics": [],
            "news_frequency_trend": "increasing" if len(recent_news) > len(older_news[:len(recent_news)]) else "decreasing"
        }

        # Process recent news (most important)
        for news in recent_news[:20]:  # Limit to 20 most recent
            news_item = {
                "date": getattr(news, 'date', None),
                "title": getattr(news, 'title', None),
                "summary": getattr(news, 'summary', None),
                "sentiment": getattr(news, 'sentiment', None),
                "source": getattr(news, 'source', None)
            }
            news_data["recent_news"].append(news_item)

            # Count sentiment distribution
            sentiment = getattr(news, 'sentiment', 'neutral')
            if sentiment and sentiment.lower() == 'positive':
                news_data["sentiment_distribution"]["positive"] += 1
            elif sentiment and sentiment.lower() == 'negative':
                news_data["sentiment_distribution"]["negative"] += 1
            else:
                news_data["sentiment_distribution"]["neutral"] += 1

        # Process older news sample for trend comparison
        for news in older_news[:10]:  # Sample of older news
            news_item = {
                "date": getattr(news, 'date', None),
                "title": getattr(news, 'title', None),
                "sentiment": getattr(news, 'sentiment', None)
            }
            news_data["older_news_sample"].append(news_item)

        # Create LLM prompt for comprehensive news analysis
        template = ChatPromptTemplate.from_messages([
            ("system", """You are an expert financial news analyst with deep knowledge of market sentiment analysis, event impact assessment, and news interpretation. Your task is to analyze the provided news data for {ticker} and generate a comprehensive trading recommendation.

Analyze the following aspects in detail:

1. **Sentiment Analysis**: Evaluate the overall sentiment tone, emotional indicators, and market perception
2. **Event Impact Analysis**: Assess the significance of specific events, announcements, and developments
3. **News Frequency Analysis**: Analyze news volume patterns, attention levels, and media coverage trends
4. **Trend Analysis**: Examine sentiment changes over time and momentum shifts
5. **Credibility Analysis**: Consider news sources, reliability factors, and potential bias

Consider the following news data:
{news_data}

Based on your analysis, provide a JSON response with the following structure:
{{
  "signal": "bullish/bearish/neutral",
  "confidence": 0-100,
  "reasoning": "detailed reasoning explaining your decision",
  "sentiment_analysis": "analysis of overall news sentiment and emotional indicators",
  "event_impact_analysis": "analysis of specific events and their market impact",
  "news_frequency_analysis": "analysis of news frequency and coverage patterns",
  "trend_analysis": "analysis of news trends and narrative evolution",
  "credibility_analysis": "analysis of news sources and reliability factors"
}}

Focus on the most important news factors that drive market sentiment and trading decisions. Consider both immediate impact and longer-term implications."""),
            ("user", "Please analyze the news data for {ticker} and provide your trading recommendation in JSON format.")
        ])

        prompt = template.invoke({
            "ticker": ticker,
            "news_data": json.dumps(news_data, indent=2, default=str)
        })

        progress.update_status("news_analyst_agent", ticker, "Getting LLM news analysis")

        # Call LLM for analysis
        def create_default_news_signal():
            return NewsAnalysisSignal(
                signal="neutral",
                confidence=0.0,
                reasoning="Error in LLM analysis; defaulting to neutral",
                sentiment_analysis="Unable to assess sentiment",
                event_impact_analysis="Unable to assess event impact",
                news_frequency_analysis="Unable to assess news frequency",
                trend_analysis="Unable to assess trends",
                credibility_analysis="Unable to assess credibility"
            )

        llm_result = call_llm(
            prompt=prompt,
            model_name=model_name,
            model_provider=model_provider,
            pydantic_model=NewsAnalysisSignal,
            agent_name="news_analyst_agent",
            default_factory=create_default_news_signal,
        )

        progress.update_status("news_analyst_agent", ticker, "Processing LLM results")

        # Process LLM results and create structured reasoning
        reasoning = {
            "sentiment_signal": {
                "signal": llm_result.signal,
                "details": llm_result.sentiment_analysis,
            },
            "event_impact_signal": {
                "signal": llm_result.signal,
                "details": llm_result.event_impact_analysis,
            },
            "frequency_signal": {
                "signal": llm_result.signal,
                "details": llm_result.news_frequency_analysis,
            },
            "trend_signal": {
                "signal": llm_result.signal,
                "details": llm_result.trend_analysis,
            },
            "credibility_signal": {
                "signal": llm_result.signal,
                "details": llm_result.credibility_analysis,
            },
        }

        # Store the analysis results
        news_analysis[ticker] = {
            "signal": llm_result.signal,
            "confidence": llm_result.confidence,
            "reasoning": reasoning,
        }

        progress.update_status("news_analyst_agent", ticker, "Done")

    # Create the news analysis message
    message = HumanMessage(
        content=json.dumps(news_analysis),
        name="news_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(news_analysis, "News Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["news_analyst_agent"] = news_analysis

    progress.update_status("news_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
