[{"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_7", "title": "MSFT dividend aristocrat status maintained", "content": "", "author": "stock_guru", "created_time": "2025-01-01T23:57:00", "url": "https://reddit.com/r/ValueInvesting/comments/mock_7", "upvotes": 231, "comments_count": 16, "ticker": "MSFT", "sentiment": "bullish", "engagement_score": 263, "source_subreddit": "ValueInvesting", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_2", "title": "Thoughts on Microsoft's latest acquisition?", "content": "", "author": "dividend_lover", "created_time": "2025-01-01T22:06:00", "url": "https://reddit.com/r/ValueInvesting/comments/mock_2", "upvotes": 326, "comments_count": 51, "ticker": "MSFT", "sentiment": "bearish", "engagement_score": 428, "source_subreddit": "ValueInvesting", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_4", "title": "MSFT dividend aristocrat status maintained", "content": "Risk assessment for MSFT - what am I missing?", "author": "finance_pro", "created_time": "2025-01-01T21:32:00", "url": "https://reddit.com/r/StockMarket/comments/mock_4", "upvotes": 264, "comments_count": 77, "ticker": "MSFT", "sentiment": "neutral", "engagement_score": 418, "source_subreddit": "StockMarket", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_6", "title": "Microsoft's AI strategy paying off", "content": "Risk assessment for MSFT - what am I missing?", "author": "finance_pro", "created_time": "2025-01-01T14:06:00", "url": "https://reddit.com/r/investing/comments/mock_6", "upvotes": 121, "comments_count": 27, "ticker": "MSFT", "sentiment": "bearish", "engagement_score": 175, "source_subreddit": "investing", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_1", "title": "Microsoft Azure growth accelerating - bullish on MSFT", "content": "Just saw the latest news about MSFT. What do you think?", "author": "dividend_lover", "created_time": "2025-01-01T09:31:00", "url": "https://reddit.com/r/SecurityAnalysis/comments/mock_1", "upvotes": 430, "comments_count": 62, "ticker": "MSFT", "sentiment": "neutral", "engagement_score": 554, "source_subreddit": "SecurityAnalysis", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_8", "title": "Thoughts on Microsoft's latest acquisition?", "content": "Risk assessment for MSFT - what am I missing?", "author": "growth_seeker", "created_time": "2025-01-01T09:27:00", "url": "https://reddit.com/r/investing/comments/mock_8", "upvotes": 336, "comments_count": 3, "ticker": "MSFT", "sentiment": "neutral", "engagement_score": 342, "source_subreddit": "investing", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_3", "title": "Thoughts on Microsoft's latest acquisition?", "content": "Technical analysis suggests MSFT might be ready for a breakout.", "author": "portfolio_king", "created_time": "2025-01-01T04:05:00", "url": "https://reddit.com/r/SecurityAnalysis/comments/mock_3", "upvotes": 20, "comments_count": 41, "ticker": "MSFT", "sentiment": "bearish", "engagement_score": 102, "source_subreddit": "SecurityAnalysis", "hashtags": null}, {"platform": "reddit", "post_id": "mock_MSFT_2025-01-01_5", "title": "MSFT earnings preview - what to expect", "content": "I've been analyzing MSFT and here's what I found...", "author": "tech_analyst", "created_time": "2025-01-01T03:37:00", "url": "https://reddit.com/r/SecurityAnalysis/comments/mock_5", "upvotes": 235, "comments_count": 68, "ticker": "MSFT", "sentiment": "bearish", "engagement_score": 371, "source_subreddit": "SecurityAnalysis", "hashtags": null}]