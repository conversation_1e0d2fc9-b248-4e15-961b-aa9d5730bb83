#!/usr/bin/env python3
"""
StockTwits 社交媒体数据收集器

该脚本从StockTwits平台获取指定股票的社交媒体帖子数据，
并按日期组织保存为JSON格式，与现有的social_media_analyst兼容。

作者: AI Assistant
创建时间: 2025-06-26
"""

import requests
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
from dataclasses import dataclass
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stocktwits_collector.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class CollectorConfig:
    """数据收集器配置类"""
    # 目标股票代码
    tickers: Optional[List[str]] = None
    # 时间范围
    start_date: str = "2025-01-01"
    end_date: str = "2025-06-15"
    # API配置
    api_base_url: str = "https://api.stocktwits.com/api/2/streams/symbol"
    max_posts_per_request: int = 30
    # 请求配置
    request_delay: float = 1.0  # 请求间隔（秒）
    max_retries: int = 3
    timeout: int = 30
    # 数据保存配置
    base_data_dir: str = "social_media_data"
    
    def __post_init__(self):
        if self.tickers is None:
            self.tickers = ["AAPL", "MSFT", "NVDA"]

class StockTwitsCollector:
    """StockTwits数据收集器"""
    
    def __init__(self, config: CollectorConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (compatible; StockTwitsCollector/1.0; +https://example.com/bot)",
            "Accept": "application/json",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        })
        
    def fetch_stocktwits_posts(self, symbol: str, max_id: Optional[str] = None) -> Dict[str, Any]:
        """
        从StockTwits API获取指定股票的帖子数据
        
        Args:
            symbol: 股票代码
            max_id: 用于分页的最大ID
            
        Returns:
            API响应数据字典
        """
        url = f"{self.config.api_base_url}/{symbol}.json"
        params = {"limit": self.config.max_posts_per_request}
        
        if max_id:
            params["max"] = max_id
            
        for attempt in range(self.config.max_retries):
            try:
                logger.info(f"正在获取 {symbol} 的帖子数据 (尝试 {attempt + 1}/{self.config.max_retries})")
                
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"成功获取 {symbol} 的 {len(data.get('messages', []))} 条帖子")
                    return data
                elif response.status_code == 429:
                    # 处理速率限制
                    wait_time = 60 * (attempt + 1)
                    logger.warning(f"遇到速率限制，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(5 * (attempt + 1))
                    
        logger.error(f"获取 {symbol} 数据失败，已达到最大重试次数")
        return {}
    
    def parse_post_data(self, raw_post: Dict[str, Any], ticker: str) -> Dict[str, Any]:
        """
        解析原始帖子数据，转换为标准格式
        
        Args:
            raw_post: 原始帖子数据
            ticker: 股票代码
            
        Returns:
            标准化的帖子数据
        """
        user_data = raw_post.get("user", {})
        
        # 提取情感分析（如果有的话）
        sentiment = "neutral"
        if "entities" in raw_post and "sentiment" in raw_post["entities"]:
            sentiment_data = raw_post["entities"]["sentiment"]
            if sentiment_data and "basic" in sentiment_data:
                sentiment = sentiment_data["basic"].lower()
        
        # 计算参与度分数
        likes = raw_post.get("likes", {}).get("total", 0)
        reshares = raw_post.get("reshares", {}).get("reshare_count", 0)
        replies = raw_post.get("reply_count", 0)
        engagement_score = likes + (reshares * 2) + (replies * 1.5)
        
        # 提取标签
        hashtags = []
        body = raw_post.get("body", "")
        hashtag_matches = re.findall(r'#(\w+)', body)
        if hashtag_matches:
            hashtags = hashtag_matches
            
        # 提取提及的股票代码
        cashtag_matches = re.findall(r'\$([A-Z]{1,5})', body)
        
        return {
            "platform": "stocktwits",
            "post_id": f"stocktwits_{raw_post.get('id', '')}",
            "title": "",  # StockTwits没有标题概念
            "content": body,
            "author": user_data.get("username", "unknown"),
            "author_id": user_data.get("id"),
            "created_time": raw_post.get("created_at", ""),
            "url": f"https://stocktwits.com/message/{raw_post.get('id', '')}",
            "upvotes": likes,
            "comments_count": replies,
            "reshares_count": reshares,
            "ticker": ticker,
            "mentioned_tickers": cashtag_matches,
            "sentiment": sentiment,
            "engagement_score": engagement_score,
            "source_platform": "stocktwits",
            "hashtags": hashtags if hashtags else None,
            "user_followers": user_data.get("followers", 0),
            "user_ideas_count": user_data.get("ideas", 0),
            "is_official": user_data.get("official", False),
            "raw_data": raw_post  # 保留原始数据以备后用
        }
    
    def filter_posts_by_date(self, posts: List[Dict[str, Any]], target_date: str) -> List[Dict[str, Any]]:
        """
        根据日期过滤帖子
        
        Args:
            posts: 帖子列表
            target_date: 目标日期 (YYYY-MM-DD)
            
        Returns:
            过滤后的帖子列表
        """
        filtered_posts = []
        target_date_obj = datetime.strptime(target_date, "%Y-%m-%d").date()
        
        for post in posts:
            try:
                # 解析帖子创建时间
                created_time = post.get("created_time", "")
                if created_time:
                    # StockTwits时间格式: 2025-06-26T06:07:54Z
                    post_date = datetime.fromisoformat(created_time.replace('Z', '+00:00')).date()
                    if post_date == target_date_obj:
                        filtered_posts.append(post)
            except (ValueError, TypeError) as e:
                logger.warning(f"解析帖子时间失败: {e}, 帖子ID: {post.get('post_id', 'unknown')}")
                continue
                
        return filtered_posts
    
    def save_daily_data(self, ticker: str, date: str, posts: List[Dict[str, Any]]):
        """
        保存每日数据到JSON文件
        
        Args:
            ticker: 股票代码
            date: 日期 (YYYY-MM-DD)
            posts: 帖子数据列表
        """
        # 创建目录结构
        ticker_dir = Path(self.config.base_data_dir) / f"{ticker}_social_media"
        ticker_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        filename = f"stocktwits_{date}.json"
        filepath = ticker_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(posts, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ 已保存 {ticker} 在 {date} 的 {len(posts)} 条帖子到: {filepath}")
            
        except Exception as e:
            logger.error(f"❌ 保存文件失败: {e}")
    
    def generate_date_range(self) -> List[str]:
        """
        生成日期范围列表
        
        Returns:
            日期字符串列表 (YYYY-MM-DD格式)
        """
        start_date = datetime.strptime(self.config.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(self.config.end_date, "%Y-%m-%d")
        
        dates = []
        current_date = start_date
        
        while current_date <= end_date:
            dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
            
        return dates
    
    def collect_historical_data(self):
        """
        收集历史数据的主要方法
        """
        if not self.config.tickers:
            logger.error("未配置股票代码列表")
            return

        logger.info("开始收集StockTwits历史数据...")
        logger.info(f"股票代码: {self.config.tickers}")
        logger.info(f"时间范围: {self.config.start_date} 到 {self.config.end_date}")

        dates = self.generate_date_range()
        total_dates = len(dates)

        for ticker in self.config.tickers:
            logger.info(f"\n开始处理股票: {ticker}")
            
            # 获取大量帖子数据
            all_posts = []
            max_id = None
            posts_collected = 0
            
            # 收集足够的帖子以覆盖整个时间范围
            while posts_collected < 1000:  # 限制最大收集数量
                data = self.fetch_stocktwits_posts(ticker, max_id)
                
                if not data or "messages" not in data:
                    logger.warning(f"未获取到 {ticker} 的有效数据")
                    break
                
                messages = data["messages"]
                if not messages:
                    logger.info(f"没有更多 {ticker} 的帖子数据")
                    break
                
                # 解析帖子数据
                for raw_post in messages:
                    parsed_post = self.parse_post_data(raw_post, ticker)
                    all_posts.append(parsed_post)
                
                posts_collected += len(messages)
                max_id = messages[-1].get("id")
                
                logger.info(f"已收集 {ticker} 的 {posts_collected} 条帖子")
                
                # 请求间隔
                time.sleep(self.config.request_delay)
            
            # 按日期分组并保存
            for i, date in enumerate(dates, 1):
                daily_posts = self.filter_posts_by_date(all_posts, date)
                
                if daily_posts:
                    self.save_daily_data(ticker, date, daily_posts)
                else:
                    logger.info(f"📅 {ticker} 在 {date} 没有找到帖子数据")
                
                # 显示进度
                progress = (i / total_dates) * 100
                logger.info(f"进度: {progress:.1f}% ({i}/{total_dates} 天)")
        
        logger.info("✅ 数据收集完成！")

    def create_summary_report(self):
        """
        创建数据收集摘要报告
        """
        if not self.config.tickers:
            logger.warning("未配置股票代码列表，无法生成摘要报告")
            return

        logger.info("\n" + "="*50)
        logger.info("数据收集摘要报告")
        logger.info("="*50)

        base_dir = Path(self.config.base_data_dir)

        for ticker in self.config.tickers:
            ticker_dir = base_dir / f"{ticker}_social_media"

            if not ticker_dir.exists():
                logger.info(f"{ticker}: 未找到数据目录")
                continue

            json_files = list(ticker_dir.glob("stocktwits_*.json"))
            total_posts = 0
            date_range = []

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        total_posts += len(data)

                    # 从文件名提取日期
                    date_match = re.search(r'stocktwits_(\d{4}-\d{2}-\d{2})\.json', json_file.name)
                    if date_match:
                        date_range.append(date_match.group(1))

                except Exception as e:
                    logger.warning(f"读取文件 {json_file} 时出错: {e}")

            if date_range:
                date_range.sort()
                logger.info(f"{ticker}:")
                logger.info(f"  - 数据文件数量: {len(json_files)}")
                logger.info(f"  - 总帖子数量: {total_posts}")
                logger.info(f"  - 日期范围: {date_range[0]} 到 {date_range[-1]}")
                logger.info(f"  - 平均每日帖子: {total_posts/len(json_files):.1f}")
            else:
                logger.info(f"{ticker}: 未找到有效数据")

        logger.info("="*50)

def load_config_from_file(config_file: str) -> CollectorConfig:
    """
    从配置文件加载配置

    Args:
        config_file: 配置文件路径

    Returns:
        CollectorConfig对象
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        return CollectorConfig(
            tickers=config_data.get("tickers", ["AAPL", "MSFT", "NVDA"]),
            start_date=config_data.get("start_date", "2025-01-01"),
            end_date=config_data.get("end_date", "2025-06-15"),
            request_delay=config_data.get("request_delay", 2.0),
            max_posts_per_request=config_data.get("max_posts_per_request", 30),
            max_retries=config_data.get("max_retries", 3),
            base_data_dir=config_data.get("base_data_dir", "social_media_data")
        )
    except FileNotFoundError:
        logger.warning(f"配置文件 {config_file} 未找到，使用默认配置")
        return CollectorConfig()
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return CollectorConfig()

def create_sample_config():
    """
    创建示例配置文件
    """
    sample_config = {
        "tickers": ["AAPL", "MSFT", "NVDA"],
        "start_date": "2025-01-01",
        "end_date": "2025-06-15",
        "request_delay": 2.0,
        "max_posts_per_request": 30,
        "max_retries": 3,
        "base_data_dir": "social_media_data",
        "description": "StockTwits数据收集器配置文件"
    }

    config_file = "stocktwits_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)

    logger.info(f"已创建示例配置文件: {config_file}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="StockTwits社交媒体数据收集器")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--create-config", action="store_true", help="创建示例配置文件")
    parser.add_argument("--tickers", nargs="+", help="股票代码列表", default=["AAPL", "MSFT", "NVDA"])
    parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)", default="2025-01-01")
    parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)", default="2025-06-15")
    parser.add_argument("--delay", type=float, help="请求间隔（秒）", default=2.0)
    parser.add_argument("--summary", action="store_true", help="仅显示数据摘要，不收集新数据")

    args = parser.parse_args()

    # 创建示例配置文件
    if args.create_config:
        create_sample_config()
        return

    # 加载配置
    if args.config:
        config = load_config_from_file(args.config)
    else:
        config = CollectorConfig(
            tickers=args.tickers,
            start_date=args.start_date,
            end_date=args.end_date,
            request_delay=args.delay
        )

    # 创建收集器
    collector = StockTwitsCollector(config)

    # 仅显示摘要
    if args.summary:
        collector.create_summary_report()
        return

    try:
        collector.collect_historical_data()
        collector.create_summary_report()
    except KeyboardInterrupt:
        logger.info("用户中断了数据收集过程")
        collector.create_summary_report()
    except Exception as e:
        logger.error(f"数据收集过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()
