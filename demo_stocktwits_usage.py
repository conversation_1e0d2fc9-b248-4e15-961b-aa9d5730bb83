#!/usr/bin/env python3
"""
StockTwits数据收集器使用演示

这个脚本演示了如何使用StockTwits数据收集器的各种功能，
包括基本使用、自定义配置、数据验证等。

作者: AI Assistant
创建时间: 2025-06-26
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path

from stocktwits_data_collector import StockTwitsCollector, CollectorConfig

def demo_basic_usage():
    """演示基本使用方法"""
    print("=" * 60)
    print("📋 演示1: 基本使用方法")
    print("=" * 60)
    
    # 创建基本配置
    config = CollectorConfig(
        tickers=["AAPL"],  # 只收集AAPL数据作为演示
        start_date="2025-06-26",  # 今天的数据
        end_date="2025-06-26",
        request_delay=1.0,  # 较短的延迟用于演示
        max_posts_per_request=10
    )
    
    print(f"📊 配置信息:")
    print(f"   股票代码: {config.tickers}")
    print(f"   时间范围: {config.start_date} 到 {config.end_date}")
    print(f"   请求延迟: {config.request_delay} 秒")
    
    # 创建收集器
    collector = StockTwitsCollector(config)
    
    print("\n🔄 开始收集数据...")
    try:
        # 收集数据
        collector.collect_historical_data()
        
        # 显示摘要
        collector.create_summary_report()
        
        print("✅ 基本使用演示完成")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")

def demo_custom_config():
    """演示自定义配置"""
    print("\n" + "=" * 60)
    print("📋 演示2: 自定义配置")
    print("=" * 60)
    
    # 创建自定义配置
    config = CollectorConfig(
        tickers=["MSFT", "NVDA"],  # 多个股票
        start_date="2025-06-25",   # 昨天和今天
        end_date="2025-06-26",
        request_delay=2.0,         # 更长的延迟
        max_posts_per_request=15,  # 更多帖子
        max_retries=5,             # 更多重试次数
        base_data_dir="demo_data"  # 自定义数据目录
    )
    
    print(f"📊 自定义配置:")
    print(f"   股票代码: {config.tickers}")
    print(f"   时间范围: {config.start_date} 到 {config.end_date}")
    print(f"   数据目录: {config.base_data_dir}")
    print(f"   最大重试: {config.max_retries}")
    
    collector = StockTwitsCollector(config)
    
    print("\n🔄 使用自定义配置收集数据...")
    try:
        collector.collect_historical_data()
        collector.create_summary_report()
        print("✅ 自定义配置演示完成")
        
    except Exception as e:
        print(f"❌ 自定义配置演示中发生错误: {e}")

def demo_api_testing():
    """演示API测试功能"""
    print("\n" + "=" * 60)
    print("📋 演示3: API连接测试")
    print("=" * 60)
    
    config = CollectorConfig(tickers=["AAPL"])
    collector = StockTwitsCollector(config)
    
    print("🔗 测试API连接...")
    
    # 测试单次API调用
    data = collector.fetch_stocktwits_posts("AAPL")
    
    if data and "messages" in data:
        messages = data["messages"]
        print(f"✅ API连接成功，获取到 {len(messages)} 条消息")
        
        if messages:
            # 显示第一条消息的详细信息
            first_message = messages[0]
            parsed = collector.parse_post_data(first_message, "AAPL")
            
            print(f"\n📄 示例帖子信息:")
            print(f"   ID: {parsed['post_id']}")
            print(f"   作者: {parsed['author']}")
            print(f"   内容: {parsed['content'][:100]}...")
            print(f"   情感: {parsed['sentiment']}")
            print(f"   点赞数: {parsed['upvotes']}")
            print(f"   参与度分数: {parsed['engagement_score']}")
            print(f"   创建时间: {parsed['created_time']}")
            
    else:
        print("❌ API连接失败或无数据")
    
    print("✅ API测试演示完成")

def demo_data_validation():
    """演示数据验证功能"""
    print("\n" + "=" * 60)
    print("📋 演示4: 数据验证")
    print("=" * 60)
    
    # 检查现有数据
    data_dir = Path("social_media_data")
    
    if not data_dir.exists():
        print("📁 数据目录不存在，先运行数据收集...")
        return
    
    print("🔍 检查已收集的数据...")
    
    total_files = 0
    total_posts = 0
    
    for ticker_dir in data_dir.iterdir():
        if ticker_dir.is_dir() and ticker_dir.name.endswith("_social_media"):
            ticker = ticker_dir.name.replace("_social_media", "")
            json_files = list(ticker_dir.glob("stocktwits_*.json"))
            
            ticker_posts = 0
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        ticker_posts += len(data)
                        total_files += 1
                        
                        # 验证数据格式
                        if data and isinstance(data, list):
                            sample_post = data[0]
                            required_fields = [
                                'platform', 'post_id', 'content', 'author',
                                'created_time', 'ticker', 'sentiment'
                            ]
                            
                            missing_fields = [field for field in required_fields 
                                            if field not in sample_post]
                            
                            if missing_fields:
                                print(f"⚠️ {json_file.name} 缺少字段: {missing_fields}")
                            else:
                                print(f"✅ {json_file.name} 数据格式正确")
                                
                except Exception as e:
                    print(f"❌ 读取 {json_file} 时出错: {e}")
            
            total_posts += ticker_posts
            print(f"📊 {ticker}: {len(json_files)} 个文件, {ticker_posts} 条帖子")
    
    print(f"\n📈 总计: {total_files} 个文件, {total_posts} 条帖子")
    print("✅ 数据验证演示完成")

def demo_config_file_usage():
    """演示配置文件使用"""
    print("\n" + "=" * 60)
    print("📋 演示5: 配置文件使用")
    print("=" * 60)
    
    # 创建示例配置文件
    demo_config = {
        "tickers": ["AAPL"],
        "start_date": "2025-06-26",
        "end_date": "2025-06-26",
        "request_delay": 1.5,
        "max_posts_per_request": 8,
        "base_data_dir": "demo_config_data"
    }
    
    config_file = "demo_config.json"
    
    # 保存配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, indent=2, ensure_ascii=False)
    
    print(f"📄 已创建配置文件: {config_file}")
    
    # 从配置文件加载
    try:
        from stocktwits_data_collector import load_config_from_file
        config = load_config_from_file(config_file)
        
        print(f"📊 从配置文件加载的设置:")
        print(f"   股票代码: {config.tickers}")
        print(f"   数据目录: {config.base_data_dir}")
        print(f"   请求延迟: {config.request_delay}")
        
        print("✅ 配置文件使用演示完成")
        
        # 清理演示文件
        Path(config_file).unlink(missing_ok=True)
        
    except Exception as e:
        print(f"❌ 配置文件演示中发生错误: {e}")

def main():
    """主演示函数"""
    print("🚀 StockTwits数据收集器功能演示")
    print("=" * 60)
    print("本演示将展示数据收集器的各种功能和使用方法")
    print("注意: 演示会进行实际的API调用，请确保网络连接正常")
    print("=" * 60)
    
    # 询问用户是否继续
    response = input("\n是否继续演示? (y/N): ").lower().strip()
    if response != 'y':
        print("演示已取消")
        return
    
    try:
        # 运行各个演示
        demo_basic_usage()
        
        time.sleep(2)  # 短暂暂停
        demo_custom_config()
        
        time.sleep(2)
        demo_api_testing()
        
        time.sleep(2)
        demo_data_validation()
        
        time.sleep(2)
        demo_config_file_usage()
        
        print("\n" + "=" * 60)
        print("🎉 所有演示完成！")
        print("=" * 60)
        print("💡 提示:")
        print("   - 查看生成的数据文件了解输出格式")
        print("   - 参考 README_StockTwits_Collector.md 获取详细文档")
        print("   - 使用 run_stocktwits_collector.py 进行交互式操作")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断了演示")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
