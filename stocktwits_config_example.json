{"description": "StockTwits数据收集器配置文件示例", "version": "1.0", "tickers": ["AAPL", "MSFT", "NVDA"], "date_range": {"start_date": "2025-01-01", "end_date": "2025-06-15", "description": "数据收集的时间范围"}, "api_settings": {"request_delay": 2.0, "max_posts_per_request": 30, "max_retries": 3, "timeout": 30, "description": "API请求相关设置"}, "data_settings": {"base_data_dir": "social_media_data", "file_naming_pattern": "stocktwits_{date}.json", "include_raw_data": true, "description": "数据保存相关设置"}, "filtering_options": {"min_engagement_score": 0, "exclude_retweets": false, "sentiment_filter": null, "description": "数据过滤选项（可选）"}, "logging": {"level": "INFO", "log_file": "stocktwits_collector.log", "console_output": true}}