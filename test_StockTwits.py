import requests
import json
from datetime import datetime
from pathlib import Path

def fetch_stocktwits_posts(symbol, min_count=3):
    url = f"https://api.stocktwits.com/api/2/streams/symbol/{symbol}.json"
    headers = {
        "User-Agent": "Mozilla/5.0 (compatible; MyBot/0.1; +https://example.com/bot)",
        "Accept": "application/json"
    }
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"❌ 获取 {symbol} 数据失败，状态码: {response.status_code}")
        return []
    
    data = response.json()
    messages = data.get("messages", [])
    return messages[:min_count]

def save_to_json(symbol, messages):
    today = datetime.utcnow().strftime("%Y-%m-%d")
    folder = Path(f"./{symbol}_StockTwits")
    folder.mkdir(parents=True, exist_ok=True)
    path = folder / f"{today}.json"
    with open(path, "w", encoding="utf-8") as f:
        json.dump(messages, f, indent=2, ensure_ascii=False)
    print(f"✅ {symbol} 的帖子已保存至：{path}")

if __name__ == "__main__":
    symbols = ["AAPL", "MSFT", "NVDA"]
    for sym in symbols:
        posts = fetch_stocktwits_posts(sym, min_count=3)
        if len(posts) >= 3:
            save_to_json(sym, posts)
        else:
            print(f"⚠️ {sym} 的帖子数量不足 3 条，仅获取到 {len(posts)} 条")
