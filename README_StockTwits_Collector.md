# StockTwits 社交媒体数据收集器

## 概述

这是一个专门用于从StockTwits平台收集股票相关社交媒体帖子的Python脚本。该脚本能够自动获取指定股票代码在特定时间范围内的所有相关讨论帖子，并按日期组织保存为JSON格式，与现有的social_media_analyst分析器完全兼容。

## 主要功能

- ✅ **多股票支持**: 同时收集AAPL、MSFT、NVDA等多只股票的数据
- ✅ **时间范围灵活**: 支持自定义开始和结束日期
- ✅ **数据格式标准化**: 输出格式与现有social_media_analyst兼容
- ✅ **错误处理完善**: 包含API限制、网络错误等异常处理
- ✅ **进度跟踪**: 实时显示数据收集进度
- ✅ **配置灵活**: 支持命令行参数和配置文件
- ✅ **数据摘要**: 自动生成收集结果摘要报告

## 安装依赖

```bash
pip install requests pathlib dataclasses
```

## 快速开始

### 1. 基本使用

```bash
# 使用默认配置收集数据
python stocktwits_data_collector.py

# 指定股票代码和日期范围
python stocktwits_data_collector.py --tickers AAPL MSFT NVDA --start-date 2025-01-01 --end-date 2025-06-15
```

### 2. 使用配置文件

```bash
# 创建示例配置文件
python stocktwits_data_collector.py --create-config

# 使用配置文件运行
python stocktwits_data_collector.py --config stocktwits_config.json
```

### 3. 查看数据摘要

```bash
# 仅显示已收集数据的摘要，不收集新数据
python stocktwits_data_collector.py --summary
```

## 命令行参数

| 参数 | 简写 | 描述 | 默认值 |
|------|------|------|--------|
| `--config` | `-c` | 配置文件路径 | 无 |
| `--create-config` | 无 | 创建示例配置文件 | 无 |
| `--tickers` | 无 | 股票代码列表 | `["AAPL", "MSFT", "NVDA"]` |
| `--start-date` | 无 | 开始日期 (YYYY-MM-DD) | `"2025-01-01"` |
| `--end-date` | 无 | 结束日期 (YYYY-MM-DD) | `"2025-06-15"` |
| `--delay` | 无 | 请求间隔（秒） | `2.0` |
| `--summary` | 无 | 仅显示数据摘要 | `False` |

## 配置文件格式

配置文件使用JSON格式，包含以下主要部分：

```json
{
  "tickers": ["AAPL", "MSFT", "NVDA"],
  "start_date": "2025-01-01",
  "end_date": "2025-06-15",
  "request_delay": 2.0,
  "max_posts_per_request": 30,
  "max_retries": 3,
  "base_data_dir": "social_media_data"
}
```

详细配置选项请参考 `stocktwits_config_example.json` 文件。

## 输出数据格式

脚本会为每个股票创建一个独立的目录，按日期保存数据：

```
social_media_data/
├── AAPL_social_media/
│   ├── stocktwits_2025-01-01.json
│   ├── stocktwits_2025-01-02.json
│   └── ...
├── MSFT_social_media/
│   ├── stocktwits_2025-01-01.json
│   └── ...
└── NVDA_social_media/
    ├── stocktwits_2025-01-01.json
    └── ...
```

每个JSON文件包含当天的所有帖子数据，格式如下：

```json
[
  {
    "platform": "stocktwits",
    "post_id": "stocktwits_619047220",
    "title": "",
    "content": "$NVDA Nvidia will be 200$ this year I can see it happening already.",
    "author": "Rashed7",
    "author_id": 5220685,
    "created_time": "2025-06-26T06:07:54Z",
    "url": "https://stocktwits.com/message/619047220",
    "upvotes": 15,
    "comments_count": 3,
    "reshares_count": 2,
    "ticker": "NVDA",
    "mentioned_tickers": ["NVDA"],
    "sentiment": "bullish",
    "engagement_score": 23.5,
    "source_platform": "stocktwits",
    "hashtags": ["NVDA", "AI"],
    "user_followers": 28,
    "user_ideas_count": 940,
    "is_official": false
  }
]
```

## 与social_media_analyst的兼容性

输出的数据格式完全兼容现有的social_media_analyst分析器，包含以下关键字段：

- `platform`: 平台标识
- `content`: 帖子内容
- `sentiment`: 情感分析结果
- `engagement_score`: 参与度分数
- `created_time`: 创建时间
- `ticker`: 相关股票代码

## 错误处理和重试机制

脚本包含完善的错误处理机制：

1. **API速率限制**: 自动检测429状态码并等待重试
2. **网络错误**: 指数退避重试策略
3. **数据解析错误**: 跳过无效数据并记录警告
4. **文件保存错误**: 详细错误日志记录

## 日志记录

脚本会同时输出到控制台和日志文件：

- 控制台: 实时进度和重要信息
- 日志文件: `stocktwits_collector.log` - 详细的操作记录

## 性能优化建议

1. **请求间隔**: 建议设置2-3秒的请求间隔以避免速率限制
2. **批量处理**: 脚本会先收集大量数据，然后按日期分组处理
3. **内存管理**: 对于大量数据，脚本会分批处理以避免内存溢出

## 故障排除

### 常见问题

1. **API速率限制**
   - 增加 `request_delay` 参数值
   - 减少 `max_posts_per_request` 参数值

2. **网络连接问题**
   - 检查网络连接
   - 增加 `timeout` 和 `max_retries` 参数值

3. **数据格式错误**
   - 检查日志文件中的详细错误信息
   - 验证API响应格式是否发生变化

### 调试模式

可以通过修改日志级别来获取更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 许可证

本脚本遵循MIT许可证，可自由使用和修改。

## 更新日志

- v1.0 (2025-06-26): 初始版本，支持基本的StockTwits数据收集功能
