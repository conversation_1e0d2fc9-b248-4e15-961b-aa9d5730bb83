from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.tools.api import get_company_news, get_insider_trades
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, List
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta


class SocialMediaAnalysisSignal(BaseModel):
    """Pydantic model for social media analysis LLM output"""
    signal: str = Field(description="Trading signal: 'bullish', 'bearish', or 'neutral'")
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    public_sentiment_analysis: str = Field(description="Analysis of public sentiment and social perception")
    insider_activity_analysis: str = Field(description="Analysis of insider trading patterns and implications")
    attention_analysis: str = Field(description="Analysis of public attention and buzz levels")
    sentiment_momentum_analysis: str = Field(description="Analysis of sentiment momentum and trend changes")
    social_influence_analysis: str = Field(description="Analysis of social influence factors and viral potential")


##### Social Media Analyst Agent #####
def social_media_analyst_agent(state: AgentState):
    """Analyzes social media sentiment and public perception using LLM to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    model_name = state["metadata"]["model_name"]
    model_provider = state["metadata"]["model_provider"]

    # Initialize social media analysis for each ticker
    social_analysis = {}

    for ticker in tickers:
        progress.update_status("social_media_analyst_agent", ticker, "Fetching social sentiment data")

        # Get company news as proxy for social sentiment (since we don't have direct social media access)
        company_news = get_company_news(
            ticker=ticker,
            end_date=end_date,
            limit=50,
            agent_name="social_media_analyst_agent",
        )

        # Get insider trades as additional social sentiment indicator
        insider_trades = get_insider_trades(
            ticker=ticker,
            end_date=end_date,
            limit=20,
            agent_name="social_media_analyst_agent",
        )

        if not company_news and not insider_trades:
            progress.update_status("social_media_analyst_agent", ticker, "Failed: No sentiment data found")
            continue

        progress.update_status("social_media_analyst_agent", ticker, "Preparing social sentiment data for LLM analysis")

        # Prepare social media/sentiment data for LLM analysis
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        recent_threshold = end_datetime - timedelta(days=7)  # Last 7 days

        # Initialize social sentiment data structure
        social_data = {
            "news_sentiment_data": {
                "total_news": len(company_news) if company_news else 0,
                "recent_news": [],
                "sentiment_distribution": {"positive": 0, "negative": 0, "neutral": 0},
                "sentiment_trend": "stable"
            },
            "insider_activity_data": {
                "total_trades": len(insider_trades) if insider_trades else 0,
                "recent_trades": [],
                "buy_sell_ratio": 0,
                "insider_sentiment": "neutral"
            },
            "attention_metrics": {
                "news_frequency": 0,
                "recent_activity_level": "normal",
                "buzz_indicators": []
            }
        }

        # Process news sentiment data (as proxy for social sentiment)
        if company_news:
            recent_news = []
            older_news = []

            for news in company_news:
                if hasattr(news, 'date') and news.date:
                    try:
                        news_date = datetime.strptime(news.date, "%Y-%m-%d")
                        if news_date >= recent_threshold:
                            recent_news.append(news)
                        else:
                            older_news.append(news)
                    except:
                        older_news.append(news)
                else:
                    older_news.append(news)

            # Process recent news for sentiment analysis
            for news in recent_news[:15]:  # Limit to 15 most recent
                news_item = {
                    "date": getattr(news, 'date', None),
                    "title": getattr(news, 'title', None),
                    "sentiment": getattr(news, 'sentiment', None),
                    "source": getattr(news, 'source', None)
                }
                social_data["news_sentiment_data"]["recent_news"].append(news_item)

                # Count sentiment distribution
                sentiment = getattr(news, 'sentiment', 'neutral')
                if sentiment and sentiment.lower() == 'positive':
                    social_data["news_sentiment_data"]["sentiment_distribution"]["positive"] += 1
                elif sentiment and sentiment.lower() == 'negative':
                    social_data["news_sentiment_data"]["sentiment_distribution"]["negative"] += 1
                else:
                    social_data["news_sentiment_data"]["sentiment_distribution"]["neutral"] += 1

            # Determine sentiment trend
            if len(recent_news) > len(older_news[:len(recent_news)]):
                social_data["news_sentiment_data"]["sentiment_trend"] = "increasing_attention"
            elif len(recent_news) < len(older_news[:len(recent_news)]):
                social_data["news_sentiment_data"]["sentiment_trend"] = "decreasing_attention"

            social_data["attention_metrics"]["news_frequency"] = len(recent_news)
            social_data["attention_metrics"]["recent_activity_level"] = "high" if len(recent_news) > 5 else "low" if len(recent_news) < 2 else "normal"

        # Process insider trading data
        if insider_trades:
            buy_trades = 0
            sell_trades = 0
            total_buy_shares = 0
            total_sell_shares = 0

            for trade in insider_trades[:10]:  # Limit to 10 most recent trades
                trade_item = {
                    "date": getattr(trade, 'date', None),
                    "transaction_shares": getattr(trade, 'transaction_shares', None),
                    "transaction_type": getattr(trade, 'transaction_type', None),
                    "insider_name": getattr(trade, 'insider_name', None)
                }
                social_data["insider_activity_data"]["recent_trades"].append(trade_item)

                # Count buy/sell activity
                shares = getattr(trade, 'transaction_shares', 0)
                if shares and shares > 0:
                    buy_trades += 1
                    total_buy_shares += shares
                elif shares and shares < 0:
                    sell_trades += 1
                    total_sell_shares += abs(shares)

            # Calculate buy/sell ratio and sentiment
            if total_buy_shares + total_sell_shares > 0:
                social_data["insider_activity_data"]["buy_sell_ratio"] = total_buy_shares / (total_buy_shares + total_sell_shares)

            if buy_trades > sell_trades:
                social_data["insider_activity_data"]["insider_sentiment"] = "bullish"
            elif sell_trades > buy_trades:
                social_data["insider_activity_data"]["insider_sentiment"] = "bearish"
            else:
                social_data["insider_activity_data"]["insider_sentiment"] = "neutral"

        # Create LLM prompt for comprehensive social media analysis
        template = ChatPromptTemplate.from_messages([
            ("system", """You are an expert social media and sentiment analyst with deep knowledge of public perception analysis, social influence patterns, and crowd psychology in financial markets. Your task is to analyze the provided social sentiment data for {ticker} and generate a comprehensive trading recommendation.

Analyze the following aspects in detail:

1. **Public Sentiment Analysis**: Evaluate overall public perception, emotional indicators, and social mood
2. **Insider Activity Analysis**: Assess insider trading patterns and their social/psychological implications
3. **Attention Analysis**: Analyze public attention levels, buzz patterns, and viral potential
4. **Sentiment Momentum Analysis**: Examine sentiment changes, momentum shifts, and crowd behavior
5. **Social Influence Analysis**: Consider social influence factors, opinion leaders, and network effects

Consider the following social sentiment data:
{social_data}

Based on your analysis, provide a JSON response with the following structure:
{{
  "signal": "bullish/bearish/neutral",
  "confidence": 0-100,
  "reasoning": "detailed reasoning explaining your decision",
  "public_sentiment_analysis": "analysis of public sentiment and social perception",
  "insider_activity_analysis": "analysis of insider trading patterns and implications",
  "attention_analysis": "analysis of public attention and buzz levels",
  "sentiment_momentum_analysis": "analysis of sentiment momentum and trend changes",
  "social_influence_analysis": "analysis of social influence factors and network effects"
}}

Focus on the most important social sentiment factors that drive market behavior and trading decisions. Consider both immediate social impact and longer-term sentiment trends."""),
            ("user", "Please analyze the social sentiment data for {ticker} and provide your trading recommendation in JSON format.")
        ])

        prompt = template.invoke({
            "ticker": ticker,
            "social_data": json.dumps(social_data, indent=2, default=str)
        })

        progress.update_status("social_media_analyst_agent", ticker, "Getting LLM social sentiment analysis")

        # Call LLM for analysis
        def create_default_social_signal():
            return SocialMediaAnalysisSignal(
                signal="neutral",
                confidence=0.0,
                reasoning="Error in LLM analysis; defaulting to neutral",
                public_sentiment_analysis="Unable to assess public sentiment",
                insider_activity_analysis="Unable to assess insider activity",
                attention_analysis="Unable to assess attention levels",
                sentiment_momentum_analysis="Unable to assess sentiment momentum",
                social_influence_analysis="Unable to assess social influence"
            )

        llm_result = call_llm(
            prompt=prompt,
            model_name=model_name,
            model_provider=model_provider,
            pydantic_model=SocialMediaAnalysisSignal,
            agent_name="social_media_analyst_agent",
            default_factory=create_default_social_signal,
        )

        progress.update_status("social_media_analyst_agent", ticker, "Processing LLM results")

        # Process LLM results and create structured reasoning
        reasoning = {
            "public_sentiment_signal": {
                "signal": llm_result.signal,
                "details": llm_result.public_sentiment_analysis,
            },
            "insider_activity_signal": {
                "signal": llm_result.signal,
                "details": llm_result.insider_activity_analysis,
            },
            "attention_signal": {
                "signal": llm_result.signal,
                "details": llm_result.attention_analysis,
            },
            "sentiment_momentum_signal": {
                "signal": llm_result.signal,
                "details": llm_result.sentiment_momentum_analysis,
            },
            "social_influence_signal": {
                "signal": llm_result.signal,
                "details": llm_result.social_influence_analysis,
            },
        }

        # Store the analysis results
        social_analysis[ticker] = {
            "signal": llm_result.signal,
            "confidence": llm_result.confidence,
            "reasoning": reasoning,
        }

        progress.update_status("social_media_analyst_agent", ticker, "Done")

    # Create the social media analysis message
    message = HumanMessage(
        content=json.dumps(social_analysis),
        name="social_media_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(social_analysis, "Social Media Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["social_media_analyst_agent"] = social_analysis

    progress.update_status("social_media_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
