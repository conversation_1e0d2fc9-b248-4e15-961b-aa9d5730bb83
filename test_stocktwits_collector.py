#!/usr/bin/env python3
"""
StockTwits数据收集器测试脚本

该脚本用于测试StockTwits数据收集器的基本功能，
包括API连接、数据解析、文件保存等核心功能。

作者: AI Assistant
创建时间: 2025-06-26
"""

import json
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import unittest
from unittest.mock import patch, MagicMock

# 导入主要的收集器类
from stocktwits_data_collector import StockTwitsCollector, CollectorConfig

class TestStockTwitsCollector(unittest.TestCase):
    """StockTwits收集器测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试配置
        self.test_config = CollectorConfig(
            tickers=["AAPL"],
            start_date="2025-01-01",
            end_date="2025-01-02",
            base_data_dir=self.temp_dir,
            request_delay=0.1,  # 测试时使用较短的延迟
            max_posts_per_request=5
        )
        
        self.collector = StockTwitsCollector(self.test_config)
    
    def tearDown(self):
        """测试后的清理"""
        # 删除临时目录
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_initialization(self):
        """测试配置初始化"""
        self.assertEqual(self.collector.config.tickers, ["AAPL"])
        self.assertEqual(self.collector.config.start_date, "2025-01-01")
        self.assertEqual(self.collector.config.end_date, "2025-01-02")
    
    def test_parse_post_data(self):
        """测试帖子数据解析"""
        # 模拟StockTwits API响应数据
        raw_post = {
            "id": 123456789,
            "body": "$AAPL looking strong today! 🚀 #bullish",
            "created_at": "2025-01-01T10:30:00Z",
            "user": {
                "id": 987654,
                "username": "test_user",
                "followers": 150,
                "ideas": 50,
                "official": False
            },
            "likes": {"total": 25},
            "reshares": {"reshare_count": 5},
            "reply_count": 8,
            "entities": {
                "sentiment": {"basic": "Bullish"}
            }
        }
        
        parsed = self.collector.parse_post_data(raw_post, "AAPL")
        
        # 验证解析结果
        self.assertEqual(parsed["platform"], "stocktwits")
        self.assertEqual(parsed["post_id"], "stocktwits_123456789")
        self.assertEqual(parsed["content"], "$AAPL looking strong today! 🚀 #bullish")
        self.assertEqual(parsed["author"], "test_user")
        self.assertEqual(parsed["ticker"], "AAPL")
        self.assertEqual(parsed["sentiment"], "bullish")
        self.assertEqual(parsed["upvotes"], 25)
        self.assertEqual(parsed["comments_count"], 8)
        self.assertEqual(parsed["reshares_count"], 5)
        self.assertIn("AAPL", parsed["mentioned_tickers"])
        self.assertIn("bullish", parsed["hashtags"])
    
    def test_filter_posts_by_date(self):
        """测试按日期过滤帖子"""
        posts = [
            {
                "post_id": "test_1",
                "created_time": "2025-01-01T10:00:00Z",
                "content": "Test post 1"
            },
            {
                "post_id": "test_2", 
                "created_time": "2025-01-02T10:00:00Z",
                "content": "Test post 2"
            },
            {
                "post_id": "test_3",
                "created_time": "2025-01-01T15:00:00Z",
                "content": "Test post 3"
            }
        ]
        
        # 过滤2025-01-01的帖子
        filtered = self.collector.filter_posts_by_date(posts, "2025-01-01")
        
        self.assertEqual(len(filtered), 2)
        self.assertEqual(filtered[0]["post_id"], "test_1")
        self.assertEqual(filtered[1]["post_id"], "test_3")
    
    def test_generate_date_range(self):
        """测试日期范围生成"""
        dates = self.collector.generate_date_range()
        
        expected_dates = ["2025-01-01", "2025-01-02"]
        self.assertEqual(dates, expected_dates)
    
    def test_save_daily_data(self):
        """测试每日数据保存"""
        test_posts = [
            {
                "post_id": "test_1",
                "content": "Test post",
                "ticker": "AAPL"
            }
        ]
        
        self.collector.save_daily_data("AAPL", "2025-01-01", test_posts)
        
        # 验证文件是否创建
        expected_path = Path(self.temp_dir) / "AAPL_social_media" / "stocktwits_2025-01-01.json"
        self.assertTrue(expected_path.exists())
        
        # 验证文件内容
        with open(expected_path, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        self.assertEqual(len(saved_data), 1)
        self.assertEqual(saved_data[0]["post_id"], "test_1")

class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """测试后的清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('stocktwits_data_collector.requests.Session.get')
    def test_api_integration(self, mock_get):
        """测试API集成（使用模拟数据）"""
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "messages": [
                {
                    "id": 123456,
                    "body": "$AAPL test message",
                    "created_at": "2025-01-01T10:00:00Z",
                    "user": {
                        "id": 789,
                        "username": "test_user",
                        "followers": 100,
                        "ideas": 20,
                        "official": False
                    },
                    "likes": {"total": 10},
                    "reshares": {"reshare_count": 2},
                    "reply_count": 3
                }
            ]
        }
        mock_get.return_value = mock_response
        
        # 创建收集器配置
        config = CollectorConfig(
            tickers=["AAPL"],
            start_date="2025-01-01",
            end_date="2025-01-01",
            base_data_dir=self.temp_dir,
            request_delay=0.1
        )
        
        collector = StockTwitsCollector(config)
        
        # 测试API调用
        result = collector.fetch_stocktwits_posts("AAPL")
        
        self.assertIn("messages", result)
        self.assertEqual(len(result["messages"]), 1)
        self.assertEqual(result["messages"][0]["id"], 123456)

def run_quick_test():
    """运行快速功能测试"""
    print("🧪 开始运行StockTwits收集器快速测试...")
    
    # 创建临时配置
    temp_dir = tempfile.mkdtemp()
    
    try:
        config = CollectorConfig(
            tickers=["AAPL"],
            start_date="2025-06-26",  # 使用今天的日期
            end_date="2025-06-26",
            base_data_dir=temp_dir,
            request_delay=1.0,
            max_posts_per_request=5
        )
        
        collector = StockTwitsCollector(config)
        
        print("✅ 配置创建成功")
        
        # 测试API连接
        print("🔗 测试API连接...")
        data = collector.fetch_stocktwits_posts("AAPL")
        
        if data and "messages" in data:
            print(f"✅ API连接成功，获取到 {len(data['messages'])} 条消息")
            
            # 测试数据解析
            if data["messages"]:
                sample_post = data["messages"][0]
                parsed = collector.parse_post_data(sample_post, "AAPL")
                print("✅ 数据解析成功")
                print(f"   示例帖子: {parsed['content'][:50]}...")
                
                # 测试数据保存
                collector.save_daily_data("AAPL", "2025-06-26", [parsed])
                print("✅ 数据保存成功")
                
                # 验证文件
                expected_file = Path(temp_dir) / "AAPL_social_media" / "stocktwits_2025-06-26.json"
                if expected_file.exists():
                    print("✅ 文件创建验证成功")
                else:
                    print("❌ 文件创建验证失败")
            else:
                print("⚠️ 未获取到消息数据")
        else:
            print("❌ API连接失败或数据格式异常")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("🧹 临时文件清理完成")
    
    print("🏁 快速测试完成")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        # 运行快速测试
        run_quick_test()
    else:
        # 运行完整的单元测试
        print("🧪 运行完整单元测试套件...")
        unittest.main(verbosity=2)

if __name__ == "__main__":
    main()
