[{"id": 619046632, "body": "$MSFT so how is does the stock price keep going up ? Who keeps being conned and buying this ridiculous overpriced stock", "created_at": "2025-06-26T05:33:16Z", "discussion": false, "user": {"id": 3307545, "username": "traderXXY", "name": "AP", "avatar_url": "https://avatars.stocktwits.com/production/3307545/thumb-1632056391.png", "avatar_url_ssl": "https://avatars.stocktwits.com/production/3307545/thumb-1632056391.png", "join_date": "2020-04-14", "official": false, "identity": "User", "classification": [], "home_country": "US", "search_regions": [], "followers": 57, "following": 32, "ideas": 4408, "watchlist_stocks_count": 109, "like_count": 544, "plus_tier": "", "premium_room": "", "trade_app": false, "trade_status": "PENDING_MFA"}, "source": {"id": 1149, "title": "StockTwits for iOS", "url": "http://www.stocktwits.com/mobile"}, "symbols": [{"id": 2735, "symbol": "MSFT", "symbol_mic": "MSFT.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Microsoft Corp", "aliases": [], "is_following": false, "watchlist_count": 512097, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "earnings_cta": "res_earn_tab", "earnings_id": "9bee54cb-c150-46cc-8f0a-846a052723f4", "trade_status": "FRACTIONAL", "sentiment_change": -6.96, "volume_change": 6.01}], "reshare_message": {"reshared_count": 1, "reshared_deleted": false, "reshared_user_deleted": false, "parent_reshared_deleted": false, "message": {"id": 619031334, "body": "$MSFT 360 🎯, Co-Pilot is a total bust.   \nhttps://www.techradar.com/pro/microsoft-is-struggling-to-sell-copilot-to-corporations-because-their-employees-want-chatgpt-instead", "created_at": "2025-06-25T23:38:46Z", "discussion": false, "user": {"id": 4790500, "username": "<PERSON><PERSON><PERSON>", "name": "Funny Money", "avatar_url": "https://avatars.stocktwits.com/production/4790500/thumb-1716475058.png", "avatar_url_ssl": "https://avatars.stocktwits.com/production/4790500/thumb-1716475058.png", "join_date": "2021-01-30", "official": false, "identity": "User", "classification": [], "home_country": "US", "search_regions": [], "followers": 570, "following": 38, "ideas": 46027, "watchlist_stocks_count": 1, "like_count": 17908, "plus_tier": "", "premium_room": "", "trade_app": false, "trade_status": "PENDING_MFA"}, "source": {"id": 2269, "title": "StockTwits Web", "url": "https://stocktwits.com"}, "symbols": [{"id": 2735, "symbol": "MSFT", "symbol_mic": "MSFT.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Microsoft Corp", "aliases": [], "is_following": false, "watchlist_count": 512097, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "earnings_cta": "res_earn_tab", "earnings_id": "9bee54cb-c150-46cc-8f0a-846a052723f4", "trade_status": "FRACTIONAL", "sentiment_change": -6.96, "volume_change": 6.01}], "links": [{"title": "Microsoft is struggling to sell Copilot to corporations - because their employees want ChatGPT instead", "url": "https://www.techradar.com/pro/microsoft-is-struggling-to-sell-copilot-to-corporations-because-their-employees-want-chatgpt-instead", "shortened_url": "https://stkt.co/Xk6_z0dT", "shortened_expanded_url": "techradar.com/pro/microsoft...", "description": "Copilot is struggling to win over workers", "image": "https://cdn.mos.cms.futurecdn.net/H8sLX8FEV6b55ru9fzJXPg.jpg", "created_at": "2025-06-25T23:38:46Z", "video_url": null, "source": {"name": "TechRadar", "website": "https://www.techradar.com"}}], "likes": {"total": 1, "user_ids": [3307545]}, "mentioned_users": [], "entities": {"media": [], "sentiment": {"basic": "Bearish"}, "discussable": null}}}, "mentioned_users": [], "entities": {"media": [], "sentiment": null, "discussable": null}}, {"id": 619044711, "body": "$SPY Memorizing a book and paraphrasing it to public for profit is NOT protected by fair use copyright law.   That essentially what $META $GOOG $MSFT etc are doing &quot;training&quot; their AI models using copyright materials.   Training essentially is memorizing the contents and regurgitating to public portions mixed up with other contents.  This is why when you &quot;regenerate&quot; using a fixed model, you eventually end up with garbage output because the AI model &quot;runs out&quot; of ideas.   https://www.cnbc.com/2025/06/25/meta-llama-ai-copyright-ruling.html", "created_at": "2025-06-26T04:09:36Z", "discussion": false, "user": {"id": 6113396, "username": "LetsgoRandy", "name": "<PERSON>", "avatar_url": "https://avatars.stocktwits.com/production/6113396/thumb-1652656059.png", "avatar_url_ssl": "https://avatars.stocktwits.com/production/6113396/thumb-1652656059.png", "join_date": "2021-11-18", "official": false, "identity": "User", "classification": [], "home_country": "US", "search_regions": [], "followers": 476, "following": 4, "ideas": 21431, "watchlist_stocks_count": 71, "like_count": 5503, "plus_tier": "", "premium_room": "", "trade_app": false, "trade_status": "PENDING_MFA"}, "source": {"id": 2269, "title": "StockTwits Web", "url": "https://stocktwits.com"}, "symbols": [{"id": 7271, "symbol": "SPY", "symbol_mic": "SPY.ARCX", "exchange": "NYSEArca", "region": "US", "title": "SPDR S&P 500 ETF", "aliases": [], "is_following": false, "watchlist_count": 586245, "has_pricing": true, "instrument_class": "ExchangeTradedFund", "live_event": false, "trade_status": "FRACTIONAL", "sentiment_change": 0.83, "volume_change": -6.28}, {"id": 7871, "symbol": "META", "symbol_mic": "META.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Meta Platforms Inc", "aliases": ["FBOOK", "MTVS", "FB"], "is_following": false, "watchlist_count": 578496, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "trade_status": "FRACTIONAL", "sentiment_change": -1.16, "volume_change": -2.71}, {"id": 2044, "symbol": "GOOG", "symbol_mic": "GOOG.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Alphabet Inc Class C", "aliases": [], "is_following": false, "watchlist_count": 297725, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "trade_status": "FRACTIONAL", "sentiment_change": 2.48, "volume_change": 5.31}, {"id": 2735, "symbol": "MSFT", "symbol_mic": "MSFT.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Microsoft Corp", "aliases": [], "is_following": false, "watchlist_count": 512100, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "earnings_cta": "res_earn_tab", "earnings_id": "9bee54cb-c150-46cc-8f0a-846a052723f4", "trade_status": "FRACTIONAL", "sentiment_change": -6.96, "volume_change": 6.01}], "conversation": {"parent_message_id": 619044711, "in_reply_to_message_id": null, "parent": true, "replies": 1}, "links": [{"title": null, "url": "https://www.cnbc.com/2025/06/25/meta-llama-ai-copyright-ruling.html", "shortened_url": "https://stkt.co/pjqxTxxk", "shortened_expanded_url": "cnbc.com/2025/06/25/meta-ll...", "description": null, "image": null, "created_at": "2025-06-26T04:09:36Z", "video_url": null, "source": {"name": "Cnbc", "website": "https://www.cnbc.com"}}], "mentioned_users": [], "entities": {"media": [], "sentiment": null, "discussable": null}}, {"id": 619044289, "body": "$QQQ officially making new all time highs today, tech is very hot right now, $SPY also very strong action. Again every single dip continues to get bought up very quickly.  \n \nWe look good for a move back up to all time highs now in my opinion, every small dip gets bought up. This continues to look like a lock out rally to me and overall very bullish action on the market.  \n \nThe big cap tech names like $META $MSFT $NFLX NVDA continue to lead and money is still flowing into those names. The weekly chart looks great to me, more consolidation would be healthy, but lots to like overall", "created_at": "2025-06-26T03:55:37Z", "discussion": false, "user": {"id": 9108898, "username": "trading_momentum", "name": "Trading Momentum", "avatar_url": "https://avatars.stocktwits.com/production/9108898/thumb-**********.png", "avatar_url_ssl": "https://avatars.stocktwits.com/production/9108898/thumb-**********.png", "join_date": "2024-05-30", "official": true, "identity": "Official", "classification": ["official"], "home_country": "US", "search_regions": [], "followers": 6394, "following": 0, "ideas": 34131, "watchlist_stocks_count": 37, "like_count": 7510, "plus_tier": "", "premium_room": "", "trade_app": false, "trade_status": "PENDING_MFA"}, "source": {"id": 2269, "title": "StockTwits Web", "url": "https://stocktwits.com"}, "symbols": [{"id": 9581, "symbol": "QQQ", "symbol_mic": "QQQ.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Invesco QQQ Trust Series 1", "aliases": ["QQQQ"], "is_following": false, "watchlist_count": 230686, "has_pricing": true, "instrument_class": "ExchangeTradedFund", "live_event": false, "trade_status": "FRACTIONAL", "sentiment_change": 0.81, "volume_change": -2.79}, {"id": 7271, "symbol": "SPY", "symbol_mic": "SPY.ARCX", "exchange": "NYSEArca", "region": "US", "title": "SPDR S&P 500 ETF", "aliases": [], "is_following": false, "watchlist_count": 586239, "has_pricing": true, "instrument_class": "ExchangeTradedFund", "live_event": false, "trade_status": "FRACTIONAL", "sentiment_change": 0.8, "volume_change": -6.32}, {"id": 7871, "symbol": "META", "symbol_mic": "META.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Meta Platforms Inc", "aliases": ["FBOOK", "MTVS", "FB"], "is_following": false, "watchlist_count": 578499, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "trade_status": "FRACTIONAL", "sentiment_change": -1.35, "volume_change": -2.59}, {"id": 2735, "symbol": "MSFT", "symbol_mic": "MSFT.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Microsoft Corp", "aliases": [], "is_following": false, "watchlist_count": 512097, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "earnings_cta": "res_earn_tab", "earnings_id": "9bee54cb-c150-46cc-8f0a-846a052723f4", "trade_status": "FRACTIONAL", "sentiment_change": -7.04, "volume_change": 5.64}, {"id": 2839, "symbol": "NFLX", "symbol_mic": "NFLX.XNAS", "exchange": "NASDAQ", "region": "US", "title": "Netflix Inc", "aliases": [], "is_following": false, "watchlist_count": 497344, "has_pricing": true, "instrument_class": "Stock", "live_event": false, "earnings_cta": "up_earn_tab", "earnings_id": "fb625eed-9fa9-4991-abe2-7f486346032a", "trade_status": "FRACTIONAL", "sentiment_change": -11.71, "volume_change": 11.45}], "likes": {"total": 2, "user_ids": [8780597, 1502834]}, "mentioned_users": [], "entities": {"media": [{"id": 4796934, "type": "image", "thumb": "https://media.stocktwits-cdn.com/api/3/media/4796934/thumb.png", "medium": "https://media.stocktwits-cdn.com/api/3/media/4796934/medium.png", "large": "https://media.stocktwits-cdn.com/api/3/media/4796934/large.png", "original": "https://media.stocktwits-cdn.com/api/3/media/4796934/original.png", "url": "https://media.stocktwits-cdn.com/api/3/media/4796934/default.png", "height": 1474, "width": 2366, "ratio": 1.60516, "provider": null, "provider_id": null}], "chart": {"id": 4796934, "type": "image", "thumb": "https://media.stocktwits-cdn.com/api/3/media/4796934/thumb.png", "medium": "https://media.stocktwits-cdn.com/api/3/media/4796934/medium.png", "large": "https://media.stocktwits-cdn.com/api/3/media/4796934/large.png", "original": "https://media.stocktwits-cdn.com/api/3/media/4796934/original.png", "url": "https://media.stocktwits-cdn.com/api/3/media/4796934/default.png", "height": 1474, "width": 2366, "ratio": 1.60516}, "sentiment": null, "discussable": null}}]